{"name": "saas-ui", "description": "The frontend stack for SaaS companies", "private": true, "type": "module", "author": "Eelco Wiersma <<EMAIL>>", "license": "MIT", "homepage": "https://saas-ui.dev/", "repository": {"type": "git", "url": "https://github.com/saas-js/saas-ui"}, "keywords": ["saas-ui", "react", "ui", "chakra-ui", "design-system", "react-components", "uikit", "accessible", "components", "emotion", "library"], "storybook": {"title": "Saas UI", "url": "https://storybook.saas-ui.dev"}, "scripts": {"w": "pnpm --filter", "dev:web": "pnpm --filter website dev", "dev:palette": "pnpm --filter palette-docs dev", "changelog:gen": "tsx scripts/changelog-generate.ts", "changelog:write": "tsx scripts/changelog-write.ts", "changelog:commit": "git add CHANGELOG.md && git commit -am 'docs: add changelog' && git push", "changeset:next": "pnpm changeset pre enter next", "changeset:version": "pnpm changelog:gen && changeset version", "publish:next": "pnpm --filter '@saas-ui/*' --filter '!@saas-ui-pro/*' --filter '!@saas-ui/cli' publish --access public --tag next", "publish": "pnpm --filter '@saas-ui/*' --filter '!@saas-ui-pro/*' --filter '!@saas-ui/cli' publish && pnpm changeset tag", "storybook": "pnpm --filter saas-ui-storybook dev", "build:storybook": "pnpm --filter saas-ui-storybook build", "prepare": "husky install", "clean": "turbo run clean", "test": "vitest", "test:watch": "vitest --watch --only<PERSON>hanged", "test:ci": "vitest --color --run", "lint": "turbo run lint", "lint:staged": "lint-staged --allow-empty --config lint-staged.config.js", "create:pkg": "hygen package chakra-component", "create:block": "pnpm hygen blocks new", "build:web": "pnpm --filter website build", "build:theme": "turbo run build --filter='./packages/saas-ui-theme'", "build:tokens": "chakra typegen packages/saas-ui-react/src/preset.ts", "build:tokens-dev": "cross-env LOCAL=1 chakra typegen packages/saas-ui-react/src/preset.ts", "build:packages": "turbo run build --filter='./packages/saas-ui-*' --filter='!./packages/pro/*' --concurrency=5 --continue", "build:packages:force": "turbo run build --filter='./packages/saas-ui-*' --filter='!./packages/pro/*' --concurrency=5 --continue --force", "watch:theme": "turbo run build --filter='./packages/saas-ui-theme' -- --watch", "clean:packages": "turbo run clean", "build:storybook-addon": "pnpm --filter @saas-ui/storybook-addon build", "build:props-docs": "pnpm typedocs && pnpm --filter @saas-ui/props-docs build", "typedocs": "tsx scripts/typedocs.ts", "preinstall": "rm -rf node_modules/odiff-bin"}, "devDependencies": {"@chakra-ui/cli": "^3.20.0", "@changesets/cli": "^2.29.4", "@manypkg/get-packages": "^2.2.2", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/node": "^22.15.30", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "browserslist": "^4.25.0", "cross-env": "^7.0.3", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.1", "eslint-import-resolver-typescript": "^4.4.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-storybook": "^0.11.6", "framer-motion": "^11.18.2", "husky": "^9.1.7", "hygen": "^6.2.11", "jsdom": "^26.0.0", "lint-staged": "^15.5.0", "next": "^15.3.3", "prettier": "^3.5.3", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "tsup": "^8.5.0", "tsx": "^4.19.3", "turbo": "2.4.4", "typescript": "^5.8.2", "vercel-submodules": "^1.0.10", "vitest": "^3.2.2"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39", "dependencies": {"@react-three/drei": "^10.1.2", "@react-three/fiber": "^9.1.2", "three": "^0.174.0"}}