<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vanta DOTS Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        #vanta-bg {
            width: 100vw;
            height: 100vh;
            background-color: #111827;
        }
        .content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 100;
            color: white;
            text-align: center;
            background: rgba(0,0,0,0.8);
            padding: 2rem;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div id="vanta-bg"></div>
    
    <div class="content">
        <h1>Vanta DOTS Effect</h1>
        <p id="status">正在加载...</p>
        <p style="font-size: 0.9rem; opacity: 0.7;">动态点阵效果</p>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r121/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanta@0.5.24/dist/vanta.dots.min.js"></script>
    
    <script>
        console.log('=== Vanta DOTS Test ===');
        
        function initVanta() {
            const statusEl = document.getElementById('status');
            
            try {
                console.log('THREE version:', THREE.REVISION);
                console.log('VANTA available:', !!window.VANTA);
                console.log('DOTS available:', !!(window.VANTA && window.VANTA.DOTS));
                
                if (!window.THREE) {
                    throw new Error('THREE.js not loaded');
                }
                
                if (!window.VANTA || !window.VANTA.DOTS) {
                    throw new Error('VANTA.DOTS not available');
                }
                
                // 使用DOTS效果
                const effect = VANTA.DOTS({
                    el: "#vanta-bg",
                    THREE: THREE,
                    mouseControls: true,
                    touchControls: true,
                    gyroControls: false,
                    minHeight: 200.00,
                    minWidth: 200.00,
                    scale: 1.00,
                    scaleMobile: 1.00,
                    color: 0x4f9eff,
                    color2: 0x00d4ff,
                    backgroundColor: 0x111827,
                    size: 4.00,
                    spacing: 30.00
                });
                
                console.log('✅ DOTS effect created:', effect);
                statusEl.textContent = '✅ DOTS背景加载成功！';
                statusEl.style.color = '#4f9eff';
                
            } catch (error) {
                console.error('❌ Error:', error);
                statusEl.textContent = '❌ 错误: ' + error.message;
                statusEl.style.color = '#ff0000';
            }
        }
        
        // 等待所有脚本加载完成
        window.addEventListener('load', function() {
            setTimeout(initVanta, 1000);
        });
    </script>
</body>
</html>
