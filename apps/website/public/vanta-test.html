<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vanta Topology Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        #vanta-bg {
            width: 100vw;
            height: 100vh;
            background-color: #111827;
        }
        .content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 100;
            color: white;
            text-align: center;
            background: rgba(0,0,0,0.7);
            padding: 2rem;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div id="vanta-bg"></div>
    
    <div class="content">
        <h1>Vanta Topology Test</h1>
        <p>如果你看到动态的网络拓扑背景，说明Vanta.js工作正常！</p>
        <p id="status">正在加载...</p>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r118/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanta@0.5.24/dist/vanta.topology.min.js"></script>
    
    <script>
        console.log('开始初始化Vanta Topology...');
        
        // 等待脚本加载完成
        window.addEventListener('load', function() {
            setTimeout(function() {
                const statusEl = document.getElementById('status');
                
                try {
                    console.log('THREE:', window.THREE);
                    console.log('VANTA:', window.VANTA);
                    console.log('VANTA.TOPOLOGY:', window.VANTA ? window.VANTA.TOPOLOGY : 'undefined');
                    
                    if (window.VANTA && window.VANTA.TOPOLOGY) {
                        const effect = window.VANTA.TOPOLOGY({
                            el: "#vanta-bg",
                            THREE: window.THREE,
                            mouseControls: true,
                            touchControls: true,
                            gyroControls: false,
                            minHeight: 200.00,
                            minWidth: 200.00,
                            scale: 1.00,
                            scaleMobile: 1.00,
                            color: 0x4f9eff,
                            backgroundColor: 0x111827
                        });
                        
                        console.log('Topology效果初始化成功！', effect);
                        statusEl.textContent = '✅ Topology背景加载成功！';
                        statusEl.style.color = '#4f9eff';
                    } else {
                        console.error('VANTA.TOPOLOGY 不可用');
                        statusEl.textContent = '❌ VANTA.TOPOLOGY 不可用';
                        statusEl.style.color = '#ff4f4f';
                    }
                } catch (error) {
                    console.error('初始化失败:', error);
                    statusEl.textContent = '❌ 初始化失败: ' + error.message;
                    statusEl.style.color = '#ff4f4f';
                }
            }, 1000);
        });
    </script>
</body>
</html>
