<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Vanta Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        #vanta-bg {
            width: 100vw;
            height: 100vh;
            background-color: #000;
        }
        .content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 100;
            color: white;
            text-align: center;
            background: rgba(0,0,0,0.8);
            padding: 2rem;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div id="vanta-bg"></div>
    
    <div class="content">
        <h1>Simple Vanta Test</h1>
        <p id="status">正在加载...</p>
    </div>

    <!-- 使用Vanta官方推荐的版本 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r121/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanta@0.5.24/dist/vanta.topology.min.js"></script>
    
    <script>
        console.log('=== Vanta Simple Test ===');
        
        function initVanta() {
            const statusEl = document.getElementById('status');
            
            try {
                console.log('THREE version:', THREE.REVISION);
                console.log('VANTA available:', !!window.VANTA);
                console.log('TOPOLOGY available:', !!(window.VANTA && window.VANTA.TOPOLOGY));
                
                if (!window.THREE) {
                    throw new Error('THREE.js not loaded');
                }
                
                if (!window.VANTA || !window.VANTA.TOPOLOGY) {
                    throw new Error('VANTA.TOPOLOGY not available');
                }
                
                // 使用最简单的配置
                const effect = VANTA.TOPOLOGY({
                    el: "#vanta-bg",
                    THREE: THREE
                });
                
                console.log('✅ Topology effect created:', effect);
                statusEl.textContent = '✅ Topology背景加载成功！';
                statusEl.style.color = '#00ff00';
                
            } catch (error) {
                console.error('❌ Error:', error);
                statusEl.textContent = '❌ 错误: ' + error.message;
                statusEl.style.color = '#ff0000';
            }
        }
        
        // 等待所有脚本加载完成
        window.addEventListener('load', function() {
            setTimeout(initVanta, 1000);
        });
    </script>
</body>
</html>
