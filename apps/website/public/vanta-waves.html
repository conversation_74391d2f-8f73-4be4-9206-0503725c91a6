<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vanta Waves Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        #vanta-bg {
            width: 100vw;
            height: 100vh;
            background-color: #000;
        }
        .content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 100;
            color: white;
            text-align: center;
            background: rgba(0,0,0,0.8);
            padding: 2rem;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div id="vanta-bg"></div>
    
    <div class="content">
        <h1>Vanta Waves Test</h1>
        <p id="status">正在加载...</p>
        <p style="font-size: 0.9rem; opacity: 0.7;">测试Waves效果是否能正常工作</p>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r121/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanta@0.5.24/dist/vanta.waves.min.js"></script>
    
    <script>
        console.log('=== Vanta Waves Test ===');
        
        function initVanta() {
            const statusEl = document.getElementById('status');
            
            try {
                console.log('THREE version:', THREE.REVISION);
                console.log('VANTA available:', !!window.VANTA);
                console.log('WAVES available:', !!(window.VANTA && window.VANTA.WAVES));
                
                if (!window.THREE) {
                    throw new Error('THREE.js not loaded');
                }
                
                if (!window.VANTA || !window.VANTA.WAVES) {
                    throw new Error('VANTA.WAVES not available');
                }
                
                // 使用Waves效果
                const effect = VANTA.WAVES({
                    el: "#vanta-bg",
                    THREE: THREE,
                    mouseControls: true,
                    touchControls: true,
                    gyroControls: false,
                    minHeight: 200.00,
                    minWidth: 200.00,
                    scale: 1.00,
                    scaleMobile: 1.00,
                    color: 0x4f9eff,
                    shininess: 30.00,
                    waveHeight: 20.00,
                    waveSpeed: 1.00,
                    zoom: 0.75
                });
                
                console.log('✅ Waves effect created:', effect);
                statusEl.textContent = '✅ Waves背景加载成功！';
                statusEl.style.color = '#00ff00';
                
            } catch (error) {
                console.error('❌ Error:', error);
                statusEl.textContent = '❌ 错误: ' + error.message;
                statusEl.style.color = '#ff0000';
            }
        }
        
        // 等待所有脚本加载完成
        window.addEventListener('load', function() {
            setTimeout(initVanta, 1000);
        });
    </script>
</body>
</html>
