'use client'

import { useEffect, useRef } from 'react'

declare global {
  interface Window {
    VANTA: any
    THREE: any
  }
}

interface VantaBackgroundProps {
  effect?: 'halo' | 'waves' | 'net' | 'particles' | 'globe' | 'stars'
  options?: Record<string, any>
  className?: string
  style?: React.CSSProperties
}

export function VantaBackground({
  effect = 'halo',
  options = {},
  className = '',
  style = {}
}: VantaBackgroundProps) {
  const vantaRef = useRef<HTMLDivElement>(null)
  const vantaEffect = useRef<any>(null)

  useEffect(() => {
    let mounted = true

    const loadFromCDN = async () => {
      try {
        // 加载 Three.js
        if (!window.THREE) {
          const threeScript = document.createElement('script')
          threeScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js'
          document.head.appendChild(threeScript)

          await new Promise((resolve, reject) => {
            threeScript.onload = resolve
            threeScript.onerror = reject
          })
        }

        // 加载对应的 Vanta 效果
        if (!window.VANTA || !window.VANTA[effect.toUpperCase()]) {
          const vantaScript = document.createElement('script')
          vantaScript.src = `https://cdn.jsdelivr.net/npm/vanta@latest/dist/vanta.${effect}.min.js`
          document.head.appendChild(vantaScript)

          await new Promise((resolve, reject) => {
            vantaScript.onload = resolve
            vantaScript.onerror = reject
          })
        }

        if (!mounted || !vantaRef.current) return

        // 初始化 Vanta 效果
        if (window.VANTA && window.VANTA[effect.toUpperCase()]) {
          vantaEffect.current = window.VANTA[effect.toUpperCase()]({
            el: vantaRef.current,
            THREE: window.THREE,
            // Halo 效果的默认配置
            ...(effect === 'halo' ? {
              mouseControls: true,
              touchControls: true,
              gyroControls: false,
              minHeight: 200.00,
              minWidth: 200.00,
              baseColor: 0x0,
              backgroundColor: 0x0,
              amplitudeFactor: 1.00,
              xOffset: 0.00,
              yOffset: 0.00,
              size: 1.00
            } : {}),
            // 用户自定义配置会覆盖默认配置
            ...options
          })
        }
      } catch (error) {
        console.error('Failed to load Vanta effect:', error)
      }
    }

    loadFromCDN()

    // 清理函数
    return () => {
      mounted = false
      if (vantaEffect.current) {
        try {
          vantaEffect.current.destroy()
        } catch (error) {
          console.error('Error destroying Vanta effect:', error)
        }
      }
    }
  }, [effect, options])

  return (
    <div
      ref={vantaRef}
      className={className}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: -1,
        pointerEvents: 'none', // 确保不会阻挡页面交互
        ...style
      }}
    />
  )
}
