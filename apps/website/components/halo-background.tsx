'use client'

import { useEffect, useRef } from 'react'

declare global {
  interface Window {
    VANTA: any
    THREE: any
  }
}

interface HaloBackgroundProps {
  options?: {
    mouseControls?: boolean
    touchControls?: boolean
    gyroControls?: boolean
    minHeight?: number
    minWidth?: number
    baseColor?: number
    backgroundColor?: number
    amplitudeFactor?: number
    xOffset?: number
    yOffset?: number
    size?: number
  }
  className?: string
  style?: React.CSSProperties
}

export function HaloBackground({ 
  options = {}, 
  className = '',
  style = {}
}: HaloBackgroundProps) {
  const vantaRef = useRef<HTMLDivElement>(null)
  const vantaEffect = useRef<any>(null)

  useEffect(() => {
    let mounted = true

    const initHalo = async () => {
      try {
        // 加载 Three.js
        if (!window.THREE) {
          await new Promise((resolve, reject) => {
            const script = document.createElement('script')
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js'
            script.onload = resolve
            script.onerror = reject
            document.head.appendChild(script)
          })
        }

        // 加载 Vanta Halo 效果
        if (!window.VANTA?.HALO) {
          await new Promise((resolve, reject) => {
            const script = document.createElement('script')
            script.src = 'https://cdn.jsdelivr.net/npm/vanta@latest/dist/vanta.halo.min.js'
            script.onload = resolve
            script.onerror = reject
            document.head.appendChild(script)
          })
        }

        if (!mounted || !vantaRef.current || !window.VANTA?.HALO) return

        // 初始化 Halo 效果
        vantaEffect.current = window.VANTA.HALO({
          el: vantaRef.current,
          THREE: window.THREE,
          mouseControls: true,
          touchControls: true,
          gyroControls: false,
          minHeight: 200.00,
          minWidth: 200.00,
          baseColor: 0x0,
          backgroundColor: 0x111827,
          amplitudeFactor: 1.00,
          xOffset: 0.00,
          yOffset: 0.00,
          size: 1.00,
          ...options
        })

        console.log('Halo background initialized successfully')
      } catch (error) {
        console.error('Failed to initialize Halo background:', error)
      }
    }

    // 延迟初始化，确保DOM已经渲染
    const timer = setTimeout(initHalo, 100)

    return () => {
      mounted = false
      clearTimeout(timer)
      if (vantaEffect.current) {
        try {
          vantaEffect.current.destroy()
          console.log('Halo background destroyed')
        } catch (error) {
          console.error('Error destroying Halo background:', error)
        }
      }
    }
  }, [options])

  return (
    <div
      ref={vantaRef}
      className={className}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: -1,
        pointerEvents: 'none',
        ...style
      }}
    />
  )
}
