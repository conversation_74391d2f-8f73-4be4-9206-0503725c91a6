'use client'

import { useEffect, useRef } from 'react'

declare global {
  interface Window {
    VANTA: any
    THREE: any
  }
}

interface TopologyBackgroundProps {
  options?: {
    mouseControls?: boolean
    touchControls?: boolean
    gyroControls?: boolean
    minHeight?: number
    minWidth?: number
    scale?: number
    scaleMobile?: number
    color?: number
    backgroundColor?: number
  }
  className?: string
  style?: React.CSSProperties
}

export function TopologyBackground({ 
  options = {}, 
  className = '',
  style = {}
}: TopologyBackgroundProps) {
  const vantaRef = useRef<HTMLDivElement>(null)
  const vantaEffect = useRef<any>(null)

  useEffect(() => {
    let mounted = true

    const initTopology = async () => {
      try {
        // 加载 Three.js
        if (!window.THREE) {
          await new Promise((resolve, reject) => {
            const script = document.createElement('script')
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js'
            script.onload = resolve
            script.onerror = reject
            document.head.appendChild(script)
          })
        }

        // 加载 Vanta Topology 效果
        if (!window.VANTA?.TOPOLOGY) {
          await new Promise((resolve, reject) => {
            const script = document.createElement('script')
            script.src = 'https://cdn.jsdelivr.net/npm/vanta@latest/dist/vanta.topology.min.js'
            script.onload = resolve
            script.onerror = reject
            document.head.appendChild(script)
          })
        }

        if (!mounted || !vantaRef.current || !window.VANTA?.TOPOLOGY) return

        // 初始化 Topology 效果
        vantaEffect.current = window.VANTA.TOPOLOGY({
          el: vantaRef.current,
          THREE: window.THREE,
          mouseControls: true,
          touchControls: true,
          gyroControls: false,
          minHeight: 200.00,
          minWidth: 200.00,
          scale: 1.00,
          scaleMobile: 1.00,
          color: 0x3fbaff,
          backgroundColor: 0x111827,
          ...options
        })

        console.log('Topology background initialized successfully')
      } catch (error) {
        console.error('Failed to initialize Topology background:', error)
      }
    }

    // 延迟初始化，确保DOM已经渲染
    const timer = setTimeout(initTopology, 100)

    return () => {
      mounted = false
      clearTimeout(timer)
      if (vantaEffect.current) {
        try {
          vantaEffect.current.destroy()
          console.log('Topology background destroyed')
        } catch (error) {
          console.error('Error destroying Topology background:', error)
        }
      }
    }
  }, [options])

  return (
    <div
      ref={vantaRef}
      className={className}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: -1,
        pointerEvents: 'none',
        ...style
      }}
    />
  )
}
