'use client'

import { useEffect, useRef } from 'react'

declare global {
  interface Window {
    VANTA: any
    THREE: any
  }
}

interface NetBackgroundProps {
  options?: {
    mouseControls?: boolean
    touchControls?: boolean
    gyroControls?: boolean
    minHeight?: number
    minWidth?: number
    scale?: number
    scaleMobile?: number
    color?: number
    backgroundColor?: number
    points?: number
    maxDistance?: number
    spacing?: number
  }
  className?: string
  style?: React.CSSProperties
}

export function NetBackground({ 
  options = {}, 
  className = '',
  style = {}
}: NetBackgroundProps) {
  const vantaRef = useRef<HTMLDivElement>(null)
  const vantaEffect = useRef<any>(null)

  useEffect(() => {
    let mounted = true

    const initNet = async () => {
      try {
        console.log('开始初始化NET背景...')
        
        // 加载 Three.js
        if (!window.THREE) {
          await new Promise((resolve, reject) => {
            const script = document.createElement('script')
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/three.js/r121/three.min.js'
            script.onload = () => {
              console.log('Three.js加载成功')
              resolve(true)
            }
            script.onerror = reject
            document.head.appendChild(script)
          })
        }

        // 加载 Vanta NET 效果
        if (!window.VANTA?.NET) {
          await new Promise((resolve, reject) => {
            const script = document.createElement('script')
            script.src = 'https://cdn.jsdelivr.net/npm/vanta@0.5.24/dist/vanta.net.min.js'
            script.onload = () => {
              console.log('Vanta NET加载成功')
              resolve(true)
            }
            script.onerror = reject
            document.head.appendChild(script)
          })
        }

        if (!mounted || !vantaRef.current || !window.VANTA?.NET) return

        // 初始化 NET 效果
        vantaEffect.current = window.VANTA.NET({
          el: vantaRef.current,
          THREE: window.THREE,
          mouseControls: true,
          touchControls: true,
          gyroControls: false,
          minHeight: 200.00,
          minWidth: 200.00,
          scale: 1.00,
          scaleMobile: 1.00,
          color: 0x4f9eff,
          backgroundColor: 0x111827,
          points: 10.00,
          maxDistance: 20.00,
          spacing: 15.00,
          ...options
        })

        console.log('NET背景初始化成功！')
      } catch (error) {
        console.error('NET背景初始化失败:', error)
      }
    }

    // 延迟初始化，确保DOM已经渲染
    const timer = setTimeout(initNet, 100)

    return () => {
      mounted = false
      clearTimeout(timer)
      if (vantaEffect.current) {
        try {
          vantaEffect.current.destroy()
          console.log('NET背景已销毁')
        } catch (error) {
          console.error('销毁NET背景时出错:', error)
        }
      }
    }
  }, [options])

  return (
    <div
      ref={vantaRef}
      className={className}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: -1,
        pointerEvents: 'none',
        ...style
      }}
    />
  )
}
