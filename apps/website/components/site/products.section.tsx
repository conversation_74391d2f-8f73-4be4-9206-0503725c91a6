import {
  Box,
  Container,
  Heading,
  Stack,
  Text,
} from '@chakra-ui/react'
import { MagicBento } from './magic-bento'

export function ProductsSection() {
  return (
    <Box as="section" py="20" borderTopWidth="1px" borderStyle="dashed">
      <Container maxW="8xl">
        <Stack gap="16" alignItems="center">
          {/* 标题区域 */}
          <Stack gap="4" alignItems="center" textAlign="center" maxW="4xl">
            <Heading as="h2" fontSize="5xl" lineHeight="1.1">
              智能投放系统流程
            </Heading>
            <Text fontSize="xl" color="fg.subtle">
              从客户需求到服务交付的完整流程，实现高效精准的保险服务匹配
            </Text>
          </Stack>

          {/* Magic Bento 流程展示区域 */}
          <MagicBento />
        </Stack>
      </Container>
    </Box>
  )
}