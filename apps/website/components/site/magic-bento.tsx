'use client'

import { Box, Grid, GridItem, Heading, Text, Stack, Icon, ResponsiveValue } from '@chakra-ui/react'
import { motion, useReducedMotion } from 'framer-motion'
import { FiSmartphone, FiCpu, FiBell, FiPhone } from 'react-icons/fi'

const MotionBox = motion(Box)
const MotionGridItem = motion(GridItem)

interface BentoCardProps {
  title: string
  description: string
  details: string[]
  icon: any
  delay: number
  colSpan?: ResponsiveValue<number>
  rowSpan?: ResponsiveValue<number>
  gradient: string
}

const BentoCard = ({
  title,
  description,
  details,
  icon,
  delay,
  colSpan = 1,
  rowSpan = 1,
  gradient
}: BentoCardProps) => {
  const shouldReduceMotion = useReducedMotion()

  return (
    <MotionGridItem
      colSpan={colSpan}
      rowSpan={rowSpan}
      initial={shouldReduceMotion ? { opacity: 1 } : { opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={shouldReduceMotion ? { duration: 0 } : {
        duration: 0.6,
        delay,
        ease: [0.25, 0.46, 0.45, 0.94]
      }}
      whileHover={shouldReduceMotion ? {} : {
        scale: 1.02,
        transition: { duration: 0.2 }
      }}
    >
      <Box
        h="full"
        p="8"
        borderRadius="2xl"
        bg={`linear-gradient(135deg, ${gradient})`}
        position="relative"
        overflow="hidden"
        cursor="pointer"
        _before={{
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          bg: 'blackAlpha.50',
          borderRadius: '2xl',
          transition: 'all 0.3s ease',
        }}
        _hover={{
          _before: {
            bg: 'blackAlpha.20',
          },
          transform: 'translateY(-4px)',
          boxShadow: '0 25px 50px rgba(0,0,0,0.15)',
        }}
        transition="all 0.3s cubic-bezier(0.4, 0, 0.2, 1)"
      >
        {/* 背景装饰 */}
        <Box
          position="absolute"
          top="-50%"
          right="-50%"
          w="200%"
          h="200%"
          opacity="0.1"
          transform="rotate(45deg)"
          bg="white"
          borderRadius="full"
        />
        
        <Stack spacing="4" position="relative" zIndex="1" h="full">
          <Box>
            <Icon 
              as={icon} 
              boxSize="12" 
              color="white" 
              mb="4"
              filter="drop-shadow(0 2px 4px rgba(0,0,0,0.2))"
            />
            <Heading 
              as="h3" 
              fontSize="2xl" 
              fontWeight="bold" 
              color="white"
              mb="3"
              textShadow="0 2px 4px rgba(0,0,0,0.3)"
            >
              {title}
            </Heading>
            <Text 
              fontSize="lg" 
              color="whiteAlpha.900"
              mb="4"
              textShadow="0 1px 2px rgba(0,0,0,0.2)"
            >
              {description}
            </Text>
          </Box>
          
          <Stack spacing="2" flex="1" justifyContent="flex-end">
            {details.map((detail, index) => (
              <Text 
                key={index}
                fontSize="sm" 
                color="whiteAlpha.800"
                textShadow="0 1px 2px rgba(0,0,0,0.2)"
              >
                {detail}
              </Text>
            ))}
          </Stack>
        </Stack>
      </Box>
    </MotionGridItem>
  )
}

export const MagicBento = () => {
  const shouldReduceMotion = useReducedMotion()

  const cards = [
    {
      title: '客户提交需求',
      description: '用户通过微信小程序填写保险需求信息',
      details: [
        '• 选择保险类型（车险、财险等）',
        '• 填写基本信息和联系方式'
      ],
      icon: FiSmartphone,
      delay: 0.1,
      colSpan: { base: 1, md: 2 },
      rowSpan: 1,
      gradient: 'rgba(59, 130, 246, 0.8), rgba(37, 99, 235, 0.9)'
    },
    {
      title: '智能算法匹配',
      description: '系统自动分析客户需求，匹配最适合的服务商',
      details: [
        '• 基于地理位置就近匹配',
        '• 根据服务商专业度排序'
      ],
      icon: FiCpu,
      delay: 0.2,
      colSpan: 1,
      rowSpan: { base: 1, md: 2 },
      gradient: 'rgba(16, 185, 129, 0.8), rgba(5, 150, 105, 0.9)'
    },
    {
      title: '实时推送通知',
      description: '向匹配的服务商实时推送客户需求',
      details: [
        '• 多渠道通知（短信、微信、APP）',
        '• 5分钟内必须响应'
      ],
      icon: FiBell,
      delay: 0.3,
      colSpan: 1,
      rowSpan: 1,
      gradient: 'rgba(245, 158, 11, 0.8), rgba(217, 119, 6, 0.9)'
    },
    {
      title: '服务商响应',
      description: '服务商主动联系客户，提供专业服务',
      details: [
        '• 平均响应时间：2分钟',
        '• 客户满意度：98.5%'
      ],
      icon: FiPhone,
      delay: 0.4,
      colSpan: 1,
      rowSpan: 1,
      gradient: 'rgba(139, 92, 246, 0.8), rgba(124, 58, 237, 0.9)'
    }
  ]

  return (
    <motion.div
      initial={shouldReduceMotion ? { opacity: 1 } : { opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={shouldReduceMotion ? { duration: 0 } : { duration: 0.8, ease: "easeOut" }}
    >
      <Box w="full" maxW="6xl" mx="auto">
        <Grid
          templateColumns={{ base: "1fr", md: "repeat(3, 1fr)" }}
          templateRows={{ base: "repeat(4, minmax(250px, auto))", md: "repeat(2, minmax(250px, auto))" }}
          gap={{ base: "4", md: "6" }}
          autoFlow="row"
        >
          {cards.map((card, index) => (
            <BentoCard key={index} {...card} />
          ))}
        </Grid>
      </Box>
    </motion.div>
  )
}
