'use client'

import { useEffect, useRef } from 'react'

export default function TopologyDemoPage() {
  const vantaRef = useRef<HTMLDivElement>(null)
  const vantaEffect = useRef<any>(null)

  useEffect(() => {
    let mounted = true

    const initTopology = async () => {
      try {
        console.log('开始加载 Topology 背景...')

        // 加载 Three.js
        if (!(window as any).THREE) {
          console.log('加载 Three.js...')
          await new Promise((resolve, reject) => {
            const script = document.createElement('script')
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js'
            script.onload = () => {
              console.log('Three.js 加载成功')
              resolve(true)
            }
            script.onerror = (error) => {
              console.error('Three.js 加载失败:', error)
              reject(error)
            }
            document.head.appendChild(script)
          })
        }

        // 加载 Vanta Topology 效果
        if (!(window as any).VANTA?.TOPOLOGY) {
          console.log('加载 Vanta Topology...')
          await new Promise((resolve, reject) => {
            const script = document.createElement('script')
            script.src = 'https://cdn.jsdelivr.net/npm/vanta@latest/dist/vanta.topology.min.js'
            script.onload = () => {
              console.log('Vanta Topology 加载成功')
              resolve(true)
            }
            script.onerror = (error) => {
              console.error('Vanta Topology 加载失败:', error)
              reject(error)
            }
            document.head.appendChild(script)
          })
        }

        if (!mounted || !vantaRef.current) {
          console.log('组件已卸载或ref不存在')
          return
        }

        if (!(window as any).VANTA?.TOPOLOGY) {
          console.error('VANTA.TOPOLOGY 不存在')
          return
        }

        console.log('初始化 Topology 效果...')

        // 初始化 Topology 效果
        vantaEffect.current = (window as any).VANTA.TOPOLOGY({
          el: vantaRef.current,
          THREE: (window as any).THREE,
          mouseControls: true,
          touchControls: true,
          gyroControls: false,
          minHeight: 200.00,
          minWidth: 200.00,
          scale: 1.00,
          scaleMobile: 1.00,
          color: 0x4f9eff,
          backgroundColor: 0x0a0a0a
        })

        console.log('Topology 背景初始化成功！', vantaEffect.current)
      } catch (error) {
        console.error('初始化 Topology 背景失败:', error)
      }
    }

    // 延迟初始化
    const timer = setTimeout(initTopology, 500)

    return () => {
      mounted = false
      clearTimeout(timer)
      if (vantaEffect.current) {
        try {
          vantaEffect.current.destroy()
          console.log('Topology 背景已销毁')
        } catch (error) {
          console.error('销毁 Topology 背景时出错:', error)
        }
      }
    }
  }, [])

  return (
    <div style={{ minHeight: '100vh', position: 'relative', overflow: 'hidden' }}>
      <div
        ref={vantaRef}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          zIndex: 0
        }}
      />
      <div style={{
        position: 'relative',
        zIndex: 10,
        padding: '2rem',
        color: 'white',
        textAlign: 'center',
        paddingTop: '5rem'
      }}>
        <h1 style={{ 
          fontSize: '4rem', 
          marginBottom: '2rem',
          background: 'linear-gradient(45deg, #4f9eff, #00d4ff)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          fontWeight: 'bold'
        }}>
          Topology Background
        </h1>
        
        <div style={{ 
          maxWidth: '800px',
          margin: '0 auto',
          marginBottom: '3rem'
        }}>
          <p style={{ 
            fontSize: '1.3rem', 
            opacity: 0.9,
            lineHeight: '1.6',
            marginBottom: '2rem'
          }}>
            动态网络拓扑背景效果已成功集成到你的网站！
          </p>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '2rem',
            marginTop: '3rem'
          }}>
            <div style={{
              padding: '2rem',
              backgroundColor: 'rgba(79, 158, 255, 0.1)',
              borderRadius: '15px',
              border: '1px solid rgba(79, 158, 255, 0.3)',
              backdropFilter: 'blur(10px)'
            }}>
              <h3 style={{ fontSize: '1.5rem', marginBottom: '1rem', color: '#4f9eff' }}>
                🎯 交互特性
              </h3>
              <ul style={{ 
                textAlign: 'left', 
                lineHeight: '1.8',
                opacity: 0.9
              }}>
                <li>鼠标跟随效果</li>
                <li>触摸设备支持</li>
                <li>响应式设计</li>
                <li>流畅的动画</li>
              </ul>
            </div>

            <div style={{
              padding: '2rem',
              backgroundColor: 'rgba(79, 158, 255, 0.1)',
              borderRadius: '15px',
              border: '1px solid rgba(79, 158, 255, 0.3)',
              backdropFilter: 'blur(10px)'
            }}>
              <h3 style={{ fontSize: '1.5rem', marginBottom: '1rem', color: '#4f9eff' }}>
                🎨 视觉效果
              </h3>
              <ul style={{ 
                textAlign: 'left', 
                lineHeight: '1.8',
                opacity: 0.9
              }}>
                <li>动态网络节点</li>
                <li>连接线动画</li>
                <li>科技感配色</li>
                <li>深度视觉层次</li>
              </ul>
            </div>

            <div style={{
              padding: '2rem',
              backgroundColor: 'rgba(79, 158, 255, 0.1)',
              borderRadius: '15px',
              border: '1px solid rgba(79, 158, 255, 0.3)',
              backdropFilter: 'blur(10px)'
            }}>
              <h3 style={{ fontSize: '1.5rem', marginBottom: '1rem', color: '#4f9eff' }}>
                ⚡ 性能优化
              </h3>
              <ul style={{ 
                textAlign: 'left', 
                lineHeight: '1.8',
                opacity: 0.9
              }}>
                <li>WebGL 硬件加速</li>
                <li>60fps 流畅运行</li>
                <li>内存自动管理</li>
                <li>移动端优化</li>
              </ul>
            </div>
          </div>
        </div>

        <div style={{
          marginTop: '4rem',
          padding: '2rem',
          backgroundColor: 'rgba(255, 255, 255, 0.05)',
          borderRadius: '20px',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(20px)'
        }}>
          <h2 style={{ 
            fontSize: '2rem', 
            marginBottom: '1rem',
            color: '#4f9eff'
          }}>
            🚀 集成完成
          </h2>
          <p style={{ 
            fontSize: '1.1rem', 
            opacity: 0.8,
            lineHeight: '1.6'
          }}>
            Topology背景效果已经成功集成到你的网站主布局中。<br/>
            现在所有页面都会显示这个动态的网络拓扑背景效果。
          </p>
        </div>
      </div>
    </div>
  )
}
