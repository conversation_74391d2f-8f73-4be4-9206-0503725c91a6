import { TopologyBackground } from '@/components/topology-background'

export default function TopologyDemoPage() {
  return (
    <div style={{ minHeight: '100vh', position: 'relative' }}>
      <TopologyBackground 
        options={{
          mouseControls: true,
          touchControls: true,
          gyroControls: false,
          minHeight: 200.00,
          minWidth: 200.00,
          scale: 1.00,
          scaleMobile: 1.00,
          color: 0x4f9eff,
          backgroundColor: 0x0a0a0a
        }}
      />
      <div style={{ 
        position: 'relative', 
        zIndex: 1, 
        padding: '2rem',
        color: 'white',
        textAlign: 'center',
        paddingTop: '5rem'
      }}>
        <h1 style={{ 
          fontSize: '4rem', 
          marginBottom: '2rem',
          background: 'linear-gradient(45deg, #4f9eff, #00d4ff)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          fontWeight: 'bold'
        }}>
          Topology Background
        </h1>
        
        <div style={{ 
          maxWidth: '800px',
          margin: '0 auto',
          marginBottom: '3rem'
        }}>
          <p style={{ 
            fontSize: '1.3rem', 
            opacity: 0.9,
            lineHeight: '1.6',
            marginBottom: '2rem'
          }}>
            动态网络拓扑背景效果已成功集成到你的网站！
          </p>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '2rem',
            marginTop: '3rem'
          }}>
            <div style={{
              padding: '2rem',
              backgroundColor: 'rgba(79, 158, 255, 0.1)',
              borderRadius: '15px',
              border: '1px solid rgba(79, 158, 255, 0.3)',
              backdropFilter: 'blur(10px)'
            }}>
              <h3 style={{ fontSize: '1.5rem', marginBottom: '1rem', color: '#4f9eff' }}>
                🎯 交互特性
              </h3>
              <ul style={{ 
                textAlign: 'left', 
                lineHeight: '1.8',
                opacity: 0.9
              }}>
                <li>鼠标跟随效果</li>
                <li>触摸设备支持</li>
                <li>响应式设计</li>
                <li>流畅的动画</li>
              </ul>
            </div>

            <div style={{
              padding: '2rem',
              backgroundColor: 'rgba(79, 158, 255, 0.1)',
              borderRadius: '15px',
              border: '1px solid rgba(79, 158, 255, 0.3)',
              backdropFilter: 'blur(10px)'
            }}>
              <h3 style={{ fontSize: '1.5rem', marginBottom: '1rem', color: '#4f9eff' }}>
                🎨 视觉效果
              </h3>
              <ul style={{ 
                textAlign: 'left', 
                lineHeight: '1.8',
                opacity: 0.9
              }}>
                <li>动态网络节点</li>
                <li>连接线动画</li>
                <li>科技感配色</li>
                <li>深度视觉层次</li>
              </ul>
            </div>

            <div style={{
              padding: '2rem',
              backgroundColor: 'rgba(79, 158, 255, 0.1)',
              borderRadius: '15px',
              border: '1px solid rgba(79, 158, 255, 0.3)',
              backdropFilter: 'blur(10px)'
            }}>
              <h3 style={{ fontSize: '1.5rem', marginBottom: '1rem', color: '#4f9eff' }}>
                ⚡ 性能优化
              </h3>
              <ul style={{ 
                textAlign: 'left', 
                lineHeight: '1.8',
                opacity: 0.9
              }}>
                <li>WebGL 硬件加速</li>
                <li>60fps 流畅运行</li>
                <li>内存自动管理</li>
                <li>移动端优化</li>
              </ul>
            </div>
          </div>
        </div>

        <div style={{
          marginTop: '4rem',
          padding: '2rem',
          backgroundColor: 'rgba(255, 255, 255, 0.05)',
          borderRadius: '20px',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(20px)'
        }}>
          <h2 style={{ 
            fontSize: '2rem', 
            marginBottom: '1rem',
            color: '#4f9eff'
          }}>
            🚀 集成完成
          </h2>
          <p style={{ 
            fontSize: '1.1rem', 
            opacity: 0.8,
            lineHeight: '1.6'
          }}>
            Topology背景效果已经成功集成到你的网站主布局中。<br/>
            现在所有页面都会显示这个动态的网络拓扑背景效果。
          </p>
        </div>
      </div>
    </div>
  )
}
