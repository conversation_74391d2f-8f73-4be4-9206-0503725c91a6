'use client'

import { useEffect, useRef } from 'react'

export default function SimpleTopologyPage() {
  const vantaRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const initVanta = async () => {
      try {
        console.log('开始初始化...')
        
        // 等待DOM完全加载
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // 加载Three.js
        if (!(window as any).THREE) {
          console.log('加载Three.js...')
          const script1 = document.createElement('script')
          script1.src = 'https://cdnjs.cloudflare.com/ajax/libs/three.js/r119/three.min.js'
          document.head.appendChild(script1)
          
          await new Promise((resolve) => {
            script1.onload = () => {
              console.log('Three.js加载完成')
              resolve(true)
            }
          })
        }
        
        // 等待一下
        await new Promise(resolve => setTimeout(resolve, 500))
        
        // 加载Vanta Topology
        if (!(window as any).VANTA?.TOPOLOGY) {
          console.log('加载Vanta Topology...')
          const script2 = document.createElement('script')
          script2.src = 'https://cdn.jsdelivr.net/npm/vanta@latest/dist/vanta.topology.min.js'
          document.head.appendChild(script2)
          
          await new Promise((resolve) => {
            script2.onload = () => {
              console.log('Vanta Topology加载完成')
              resolve(true)
            }
          })
        }
        
        // 等待一下
        await new Promise(resolve => setTimeout(resolve, 500))
        
        if (vantaRef.current && (window as any).VANTA?.TOPOLOGY) {
          console.log('开始初始化Topology效果...')
          
          const effect = (window as any).VANTA.TOPOLOGY({
            el: vantaRef.current,
            THREE: (window as any).THREE,
            mouseControls: true,
            touchControls: true,
            gyroControls: false,
            minHeight: 200.00,
            minWidth: 200.00,
            scale: 1.00,
            scaleMobile: 1.00,
            color: 0x4f9eff,
            backgroundColor: 0x111827
          })
          
          console.log('Topology效果初始化成功！', effect)
        } else {
          console.error('初始化失败 - ref或VANTA不存在')
          console.log('vantaRef.current:', vantaRef.current)
          console.log('VANTA:', (window as any).VANTA)
          console.log('TOPOLOGY:', (window as any).VANTA?.TOPOLOGY)
        }
        
      } catch (error) {
        console.error('初始化过程中出错:', error)
      }
    }

    initVanta()
  }, [])

  return (
    <div style={{ 
      width: '100vw', 
      height: '100vh', 
      margin: 0, 
      padding: 0,
      position: 'relative',
      backgroundColor: '#111827'
    }}>
      <div
        ref={vantaRef}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          backgroundColor: '#111827'
        }}
      />
      
      <div style={{
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        zIndex: 100,
        color: 'white',
        textAlign: 'center',
        backgroundColor: 'rgba(0,0,0,0.5)',
        padding: '2rem',
        borderRadius: '10px'
      }}>
        <h1 style={{ fontSize: '2rem', marginBottom: '1rem' }}>
          Simple Topology Test
        </h1>
        <p>如果背景显示了网络拓扑效果，说明成功了！</p>
        <p style={{ fontSize: '0.9rem', opacity: 0.7, marginTop: '1rem' }}>
          请打开浏览器控制台查看加载日志
        </p>
      </div>
    </div>
  )
}
