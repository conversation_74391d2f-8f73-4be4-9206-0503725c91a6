import { TopologyBackground } from '@/components/topology-background'

export default function TestTopologyPage() {
  return (
    <div style={{ minHeight: '100vh', position: 'relative' }}>
      <TopologyBackground 
        options={{
          mouseControls: true,
          touchControls: true,
          gyroControls: false,
          minHeight: 200.00,
          minWidth: 200.00,
          scale: 1.00,
          scaleMobile: 1.00,
          color: 0x3fbaff,
          backgroundColor: 0x111827
        }}
      />
      <div style={{ 
        position: 'relative', 
        zIndex: 1, 
        padding: '2rem',
        color: 'white',
        textAlign: 'center',
        paddingTop: '10rem'
      }}>
        <h1 style={{ fontSize: '3rem', marginBottom: '1rem' }}>
          Topology Background Test
        </h1>
        <p style={{ fontSize: '1.2rem', opacity: 0.8 }}>
          如果你能看到动态的网络拓扑效果背景，说明集成成功了！
        </p>
        <p style={{ fontSize: '1rem', opacity: 0.6, marginTop: '2rem' }}>
          移动鼠标可以与背景进行交互
        </p>
        <div style={{ 
          marginTop: '3rem',
          padding: '2rem',
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          borderRadius: '10px',
          backdropFilter: 'blur(10px)'
        }}>
          <h2 style={{ fontSize: '1.5rem', marginBottom: '1rem' }}>
            Topology效果特点
          </h2>
          <ul style={{ 
            textAlign: 'left', 
            maxWidth: '600px', 
            margin: '0 auto',
            lineHeight: '1.6'
          }}>
            <li>动态的网络节点连接</li>
            <li>响应鼠标移动的交互效果</li>
            <li>科技感十足的视觉体验</li>
            <li>适合科技、数据、网络相关的网站</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
