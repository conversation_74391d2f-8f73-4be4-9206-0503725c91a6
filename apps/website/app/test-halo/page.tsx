import { HaloBackground } from '@/components/halo-background'

export default function TestHaloPage() {
  return (
    <div style={{ minHeight: '100vh', position: 'relative' }}>
      <HaloBackground 
        options={{
          mouseControls: true,
          touchControls: true,
          gyroControls: false,
          minHeight: 200.00,
          minWidth: 200.00,
          baseColor: 0x0,
          backgroundColor: 0x111827,
          amplitudeFactor: 1.00,
          xOffset: 0.00,
          yOffset: 0.00,
          size: 1.00
        }}
      />
      <div style={{ 
        position: 'relative', 
        zIndex: 1, 
        padding: '2rem',
        color: 'white',
        textAlign: 'center',
        paddingTop: '10rem'
      }}>
        <h1 style={{ fontSize: '3rem', marginBottom: '1rem' }}>
          Halo Background Test
        </h1>
        <p style={{ fontSize: '1.2rem', opacity: 0.8 }}>
          如果你能看到动态的Halo效果背景，说明集成成功了！
        </p>
        <p style={{ fontSize: '1rem', opacity: 0.6, marginTop: '2rem' }}>
          移动鼠标可以与背景进行交互
        </p>
      </div>
    </div>
  )
}
